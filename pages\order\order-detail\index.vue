<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>

		<view class="margin-bottom-bar" v-if="orderInfo != null">
			<view class="bg-white padding">
				<view :class="'text-red text-bold text-xl cuIcon-' + (orderInfo.status == null && orderInfo.isPay == '0' ? 'pay' : orderInfo.status == '1' ? 'send' : orderInfo.status == '2' ? 'deliver' : orderInfo.status == '3' ? 'evaluate' : orderInfo.status == '4' ? 'upstage' : orderInfo.status == '5' ? 'roundclose' : '')"><text class="margin-left-xs">{{orderInfo.statusDesc}}</text></view>
				<view class="text-gray text-sm margin-top-xs" v-if="orderInfo.isPay == '0' && !orderInfo.status">请在<count-down :outTime="1000 * orderInfo.outTime"
					 @countDownDone="countDownDone"></count-down>内付款，超时订单将自动取消</view>
				<view class="text-gray text-sm margin-top-xs" v-if="orderInfo.status == '2'">还剩<count-down :outTime="1000 * orderInfo.outTime"
					 @countDownDone="countDownDone"></count-down>自动确认</view>
			</view>
			<view class="cu-list cu-card no-card menu-avatar margin-top-xs">
				<navigator class="cu-item address-bg" :url="'/pages/order/order-logistics/index?id=' + orderInfo.orderLogistics.id" v-if="orderInfo.deliveryWay == '1' && (orderInfo.status == '2' || orderInfo.status == '3' || orderInfo.status == '4')">
					<view class="cu-avatar round cuIcon-deliver_fill bg-red align-center"></view>
					<view class="loc-content align-center">
						<view class="flex align-center">
							<view class="text-blue">{{orderInfo.orderLogistics.statusDesc}}</view>
							<view class="text-gray text-sm margin-left-sm">{{orderInfo.orderLogistics.logisticsDesc}}</view>
							<view class="cuIcon-right text-gray text-xs"></view>
						</view>
						<view class="text-gray text-sm overflow-2 loc-info padding-right-sm" v-if="orderInfo.orderLogistics.message">{{orderInfo.orderLogistics.message}}</view>
					</view>
				</navigator>
				<view class="cu-item address-bg" v-if="orderInfo.deliveryWay == '1' && orderInfo.orderLogistics">
					<view class="cu-avatar round cuIcon-locationfill bg-orange align-center"></view>
					<view class="loc-content align-center">
						<view class="flex align-center">
							<view class="text-black">{{orderInfo.orderLogistics.userName}}</view>
							<view class="text-gray text-sm margin-left-sm">{{orderInfo.orderLogistics.telNum}}</view>
						</view>
						<view class="text-gray text-sm overflow-2 loc-info">{{orderInfo.orderLogistics.address}}</view>
					</view>
				</view>
				<view class="cu-item address-bg" v-if="orderInfo.deliveryWay == '2'">
					<view class="cu-avatar round cuIcon-locationfill bg-orange align-center"></view>
					<view class="loc-content align-center">
						<view class="flex align-center">
							<view class="cu-tag radius line-red sm">上门自提</view>
							<view class="text-gray text-sm margin-left-sm">{{orderInfo.shopInfo.phone}}</view>
						</view>
						<view class="text-gray text-sm margin-top-xs overflow-2 loc-info">{{orderInfo.shopInfo.address}}</view>
					</view>
				</view>
			</view>
			<view class="cu-card no-card article margin-top-xs">
				<view class="cu-item ">
<!--					<navigator class="padding-lr padding-top padding-bottom-sm align-center" hover-class="none" :url="'/pages/shop/shop-detail/index?id=' + orderInfo.shopInfo.id">
						<view class="cu-avatar sm radius" :style="'background-image:url(' + orderInfo.shopInfo.imgUrl + ')'"></view>
						<text class="text-df margin-left-sm">{{orderInfo.shopInfo.name}}</text>
						<text class="cuIcon-right text-gray text-xs"></text>
					</navigator>-->
					<view class="cu-list menu">
						<view v-for="(item, index) in orderInfo.listOrderItem" :key="index">
<!--							<navigator hover-class="none" :url="'/pages/goods/goods-detail/index?id=' + item.spuId" class="cu-item">-->
								<view class="flex margin-top-sm">
									<view class="content response align-center">
										<image :src="item.picUrl ? item.picUrl : '/static/public/img/no_pic.png'" mode="aspectFill"
										class="row-img"></image>
										<view class="desc row-info block">
											<view class="text-black text-sm overflow-2">{{item.spuName}}</view>
											<view class="text-gray margin-top-xs text-sm overflow-2" v-if="item.specInfo">{{item.specInfo}}</view>
											<view class="flex justify-between align-center">
												<view class="text-price text-gray text-sm margin-top-sm">{{item.salesPrice}}</view>
												<view class="text-gray text-sm margin-top-sm">x{{item.quantity}}</view>
											</view>
										</view>
									</view>
								</view>
<!--							</navigator>-->
							<view class="cu-item text-right padding-sm margin-right-xs">
								<navigator class="cu-btn line sm" :url="'/pages/order/order-refunds/submit/index?orderItemId=' + item.id" v-if="orderInfo.isPay == '1' && item.status == '0'&& orderInfo.status != '3'&& orderInfo.status != '12'">申请售后</navigator>
								<navigator class="cu-btn line sm text-orange" :url="'/pages/order/order-refunds/form/index?orderItemId=' + item.id"
								 v-if="orderInfo.isPay == '1' && item.status != '0'">{{item.statusDesc}}</navigator>
							</view>
						</view>
						<view class="cu-item">
							<view class="">
								<text class="text-gray text-sm">订单金额</text>
							</view>
							<view class="text-sm text-gray">
								<text class="text-price">{{orderInfo.salesPrice}}</text>
							</view>
						</view>

						<view class="cu-item margin-top-xs" v-if="orderInfo.paymentCouponPrice">
							<view class="">
								<text class="text-gray text-sm">优惠券抵扣金额</text>
							</view>
							<view class="text-sm text-gray">-<text class="text-price">{{orderInfo.paymentCouponPrice}}</text>
							</view>
						</view>
						<view class="cu-item margin-top-xs" v-if="orderInfo.paymentPoints">
							<view class="">
								<text class="text-gray text-sm">积分抵扣金额</text>
							</view>
							<view class="text-sm text-gray">-<text class="text-price">{{orderInfo.paymentPointsPrice}}</text>
							</view>
						</view>
						<view class="cu-item margin-top-xs">
							<view class="">
								<text class="text-gray text-sm">支付金额</text>
							</view>
							<view class="margin-top-xs">
								<text class="text-gray text-sm" v-if="orderInfo.orderType != '0'">{{orderInfo.orderType == '1' ? '砍价后' : orderInfo.orderType == '2' ? '拼团价' : orderInfo.orderType == '3' ? '秒杀价' : ''}}</text>
								<text class="text-price text-red text-df text-bold margin-left-sm">{{orderInfo.paymentPrice}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="cu-card no-card margin-top-xs">
				<view class="cu-item order-information">
					<view class="cu-bar bg-white">
						<view class="text-df margin-left-sm">
							<text class="cuIcon-titles" :class="'text-'+theme.themeColor"></text>订单信息</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="margin-left flex-sub text-sm text-gray">订单编号:</text>
						<view class="flex-twice text-sm text-gray">{{orderInfo.orderNo}}<button class="cu-btn sm margin-left-xl" @tap="copyData(orderInfo.orderNo)" :data-data="orderInfo.orderNo">复制</button>
						</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="margin-left flex-sub text-sm text-gray">创建时间:</text>
						<view class="flex-twice text-sm text-gray">{{orderInfo.createTime}}</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center" v-if="orderInfo.paymentTime">
						<text class="margin-left flex-sub text-sm text-gray">付款时间:</text>
						<view class="flex-twice text-sm text-gray">{{orderInfo.paymentTime}}</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="margin-left flex-sub text-sm text-gray">订单来源:</text>
						<view class="flex-twice text-sm text-gray">{{orderInfo.appType=='MA' ? '小程序' : orderInfo.appType=='H5-WX' ? '公众号H5' : orderInfo.appType=='H5' ? '普通H5' : orderInfo.appType=='APP' ? 'APP' : ''}}</view>
					</view>
				</view>
			</view>
			<view class="cu-card no-card margin-top-xs" v-if="orderInfo.userMessage">
				<view class="cu-item cu-form-group order-information align-start">
					<view class="cu-bar bg-white">
						<view class="text-df">给卖家留言:</view>
					</view>
					<textarea class="text-sm text-gray" readonly :value="orderInfo.userMessage"></textarea>
				</view>
			</view>
		</view>
		<view class="cu-bar bg-white border foot">
			<order-operate class="response" :orderInfo="orderInfo" :callPay="callPay" :contact="true"
			 @orderCancel="orderCancel"
			 @orderReceive="orderCancel"
			 @orderDel="orderDel"
			 @unifiedOrder="unifiedOrder"
			 @inviteFriends="inviteFriends"
			 :inviteLoading="inviteLoading"></order-operate>
		</view>

		<!-- 邀请好友确认对话框 -->
		<view v-if="showShareDialog" class="share-mask" @tap="closeShareDialog">
			<view class="share-dialog" @tap.stop>
				<view class="share-title">{{inviteDialogContent}}</view>
				<view class="share-buttons">
					<button class="share-btn cancel-btn" @tap="closeShareDialog">取消</button>
					<button class="share-btn confirm-btn" open-type="share">确认邀请</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	import api from 'utils/api'
	import orderOperate from "components/order-operate/index";
	import countDown from "components/count-down/index";
	import util from 'utils/util'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				orderInfo: null,
				id: null,
				callPay: false, //是否直接调起支付
				// 邀请好友相关数据
				showShareDialog: false,
				inviteDialogContent: '要邀请好友参与拼团活动吗？',
				currentShareOrder: null, // 当前要分享的订单
				pageDivData: null, // 页面分享数据
				inviteLoading: false // 邀请好友按钮加载状态
			};
		},

		components: {
			orderOperate,
			countDown
		},
		props: {},

		onShow() {
			app.initPage().then(res => {
				this.orderGet(this.id);
			});
		},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			this.id = options.id;
			if (options.callPay) {
				this.callPay = true;
			}
		},

		methods: {
			orderGet(id) {
				let that = this;
				api.orderGet(id).then(res => {
					let orderInfo = res.data;
					if (!orderInfo) {
						uni.redirectTo({
							url: '/pages/order/order-list/index'
						});
					}
					this.orderInfo = orderInfo;
					setTimeout(function() {
						that.callPay = false;
					}, 4000);
				});
			},

			//复制内容
			copyData(value) {
				uni.setClipboardData({
					data: value,
					success: function () {
						uni.showToast({
							title: '复制成功',
							icon: 'none',
						});
					}
				});
			},
			orderCancel() {
				let id = this.orderInfo.id;
				this.orderGet(id);
			},
			orderDel() {
				uni.navigateBack({delta: 1});
			},
			unifiedOrder() {
				//支付成功跳转到支付结果页面
				uni.redirectTo({
					url: '/pages/order/order-pay-result/index?id=' + this.id
				});
			},
			countDownDone() {
				this.orderGet(this.id);
			},

			/**
			 * 邀请好友事件处理
			 */
			inviteFriends(event) {
				const orderInfo = this.orderInfo;
				console.log("邀请好友，订单信息:", orderInfo);

				// 设置加载状态
				this.inviteLoading = true;

				// 保存当前要分享的订单
				this.currentShareOrder = orderInfo;

				// 更新分享参数并获取分享数据
				this.updateShareParams(orderInfo);
			},

			/**
			 * 更新分享参数并获取分享数据
			 */
			updateShareParams(orderInfo) {
				// 准备API请求参数
				const params = {
					id: orderInfo.pageId,
					tenantid: orderInfo.tenantId
				};

				console.log("获取页面分享数据, 参数:", params);

				// 调用API获取页面设计数据
				api.pagedevise(params)
					.then(res => {
						console.log("页面设计数据获取成功:", res);
						this.pageDivData = res.data;

						// 更新对话框内容
						if (this.pageDivData && this.pageDivData.pageBase && this.pageDivData.pageBase.shareTitle) {
							this.inviteDialogContent = `要邀请好友参与${this.pageDivData.pageBase.shareTitle}活动吗？`;
						}

						// 显示分享对话框
						this.showShareDialog = true;

						// 恢复按钮状态
						this.inviteLoading = false;
					})
					.catch(err => {
						console.error("获取页面分享数据失败:", err);
						// 即使获取失败也显示默认对话框
						this.showShareDialog = true;

						// 恢复按钮状态
						this.inviteLoading = false;
					});
			},

			/**
			 * 关闭分享对话框
			 */
			closeShareDialog() {
				this.showShareDialog = false;
				this.currentShareOrder = null;
				this.inviteLoading = false; // 恢复按钮状态
			}

		},

		/**
		 * 分享功能配置
		 */
		onShareAppMessage() {
			// 如果没有当前分享订单，返回默认配置
			if (!this.currentShareOrder) {
				return {
					title: '邀请您参与拼团',
					path: 'pages/groupon/index'
				};
			}

			// 如果没有分享数据，返回默认配置
			if (!this.pageDivData || !this.pageDivData.pageBase) {
				return {
					title: '邀请您参与拼团',
					path: 'pages/groupon/index'
				};
			}

			// 获取分享标题、描述和图片URL
			let title = this.pageDivData.pageBase.shareTitle || '邀请您参与拼团';
			let imageUrl = this.pageDivData.pageBase.shareImgUrl || '';

			// 构建当前页面路径
			let path = 'pages/groupon/index';

			// 添加当前页面的查询参数
			const query = [];
			if (this.currentShareOrder.pageId) query.push('page_id=' + this.currentShareOrder.pageId);
			if (this.currentShareOrder.appId) query.push('app_id=' + this.currentShareOrder.appId);
			if (this.currentShareOrder.tenantId) query.push('tenant_id=' + this.currentShareOrder.tenantId);
			if (this.currentShareOrder.groupId) query.push('group_id=' + this.currentShareOrder.groupId);

			// 添加用户标识
			const userInfo = uni.getStorageSync('user_info');
			if (userInfo && userInfo.id) {
				query.push('sharer_friend_id=' + userInfo.id);
			}

			// 拼接查询参数
			if (query.length > 0) {
				path = path + '?' + query.join('&');
			}

			console.log('分享配置:', { title, path, imageUrl });

			return {
				title: title,
				path: path,
				imageUrl: imageUrl,
				success: function(res) {
					if (res.errMsg == 'shareAppMessage:ok') {
						console.log('分享成功:', res.errMsg);
					}
				},
				fail: function(res) {
					console.log('分享失败:', res);
				}
			};
		}
	};
</script>
<style>
	.address-bg{
		min-height: 180rpx;
	}

	.row-img {
		width: 200rpx !important;
		height: 200rpx !important;
		border-radius: 10rpx
	}

	.loc-content {
		width: 84% !important;
	}

	.loc-info {
		line-height: 1.4em
	}

	.cu-list.menu>.cu-item:after {
		border-bottom: unset !important;
	}

	.cu-list.menu>.cu-item {
		min-height: unset !important;
	}

	.order-information{
		padding-bottom: 100rpx;
	}

	/* 分享对话框遮罩 */
	.share-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	/* 分享对话框 */
	.share-dialog {
		background: white;
		border-radius: 10px;
		padding: 30px;
		width: 280px;
		text-align: center;
		margin: 0 20px;
	}

	/* 对话框标题 */
	.share-title {
		font-size: 16px;
		color: #333;
		margin-bottom: 30px;
		line-height: 1.5;
	}

	/* 按钮容器 */
	.share-buttons {
		display: flex;
		justify-content: space-between;
		gap: 15px;
	}

	/* 按钮样式 */
	.share-btn {
		flex: 1;
		height: 40px;
		border-radius: 20px;
		border: none;
		font-size: 14px;
		cursor: pointer;
		line-height: 40px;
	}

	/* 取消按钮 */
	.cancel-btn {
		background: #f5f5f5;
		color: #666;
	}

	/* 确认按钮 */
	.confirm-btn {
		background: #007aff;
		color: white;
	}

	/* 移除按钮默认边框 */
	.confirm-btn::after,
	.cancel-btn::after {
		border: none;
	}
</style>
